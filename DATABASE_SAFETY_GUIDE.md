# 🛡️ 数据库安全操作指南

## ⚠️ 重要安全原则

**本项目严格遵循只执行DML操作，绝不执行DDL操作的原则，以保护远程数据库结构安全。**

## 📋 操作分类

### ✅ 允许的DML操作 (Data Manipulation Language)
- `SELECT` - 查询数据
- `INSERT` - 插入数据
- `UPDATE` - 更新数据
- `DELETE` - 删除数据

### ❌ 禁止的DDL操作 (Data Definition Language)
- `CREATE TABLE` - 创建表
- `DROP TABLE` - 删除表
- `ALTER TABLE` - 修改表结构
- `CREATE INDEX` - 创建索引
- `DROP INDEX` - 删除索引
- `TRUNCATE` - 清空表

## 🔍 项目中的安全措施

### 1. Entity Framework 配置
- **禁用自动迁移**: 项目不包含任何迁移文件
- **禁用数据库创建**: 生产代码中不使用 `EnsureCreated()` 或 `EnsureDeleted()`
- **只映射现有表**: DbContext 只映射已存在的数据库表

### 2. 测试环境隔离
- **单元测试**: 使用 Mock 对象，不涉及真实数据库
- **集成测试**: 使用内存数据库 (`UseInMemoryDatabase`)
- **E2E测试**: 使用独立的内存数据库实例

### 3. 代码审查要点
检查以下可能的DDL操作：
```csharp
// ❌ 禁止使用
context.Database.EnsureCreated();
context.Database.EnsureDeleted();
context.Database.Migrate();
context.Database.ExecuteSqlRaw("CREATE TABLE...");
context.Database.ExecuteSqlRaw("DROP TABLE...");
context.Database.ExecuteSqlRaw("ALTER TABLE...");

// ✅ 允许使用
context.Users.Add(user);           // INSERT
context.Users.Update(user);        // UPDATE
context.Users.Remove(user);        // DELETE
context.Users.Where(...).ToList(); // SELECT
```

## 🏗️ 数据库表结构管理

### 表结构变更流程
1. **数据库管理员负责**: 所有表结构变更由DBA执行
2. **代码适配**: 开发人员只需更新实体模型以匹配现有表结构
3. **测试验证**: 使用内存数据库测试新的实体映射

### 现有表结构
项目当前映射以下已存在的表：
- `cursor_user_activity_summary` - 用户活动汇总表
- `xmlybep_user_identity_mapping` - 用户身份映射表

## 🧪 测试策略

### 单元测试
```csharp
// ✅ 正确的测试方式 - 使用Mock
var mockRepository = new Mock<IUserRepository>();
mockRepository.Setup(r => r.GetByIdAsync(1))
          .ReturnsAsync(new User { Id = 1, Name = "Test" });
```

### 集成测试
```csharp
// ✅ 正确的测试方式 - 使用内存数据库
services.AddDbContext<ApplicationDbContext>(options =>
    options.UseInMemoryDatabase("TestDatabase_" + Guid.NewGuid()));
```

### ❌ 错误的测试方式
```csharp
// ❌ 绝对禁止 - 可能影响远程数据库
context.Database.EnsureCreated();  // 可能创建表
context.Database.EnsureDeleted();  // 可能删除表
```

## 🔧 开发最佳实践

### 1. 实体模型设计
- 只映射现有数据库表
- 使用 `[Table]` 特性指定确切的表名
- 使用 `[Column]` 特性指定确切的列名

### 2. DbContext 配置
```csharp
protected override void OnModelCreating(ModelBuilder modelBuilder)
{
    // ✅ 只配置映射关系，不创建表结构
    modelBuilder.Entity<User>(entity =>
    {
        entity.ToTable("existing_table_name");
        entity.HasKey(e => e.Id);
        entity.Property(e => e.Name).HasColumnName("user_name");
    });
}
```

### 3. Repository 实现
```csharp
// ✅ 只执行DML操作
public async Task<User> AddAsync(User user)
{
    _context.Users.Add(user);  // INSERT
    await _context.SaveChangesAsync();
    return user;
}

public async Task UpdateAsync(User user)
{
    _context.Users.Update(user);  // UPDATE
    await _context.SaveChangesAsync();
}
```

## 🚨 紧急响应

如果发现代码中存在DDL操作：
1. **立即停止**: 停止相关功能的部署
2. **代码审查**: 检查所有可能的DDL操作
3. **测试验证**: 确保修复后的代码只执行DML操作
4. **文档更新**: 更新相关文档和指南

## 📞 联系方式

如有数据库结构变更需求，请联系：
- **数据库管理员**: 负责DDL操作
- **开发团队**: 负责代码适配和DML操作

---

**记住：我们的代码只操作数据，永不修改结构！**
