#!/bin/bash

# 单元测试运行脚本
# 使用方法: ./run-unit-tests.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== PerfStat 单元测试套件 ===${NC}"
echo ""

# 构建项目
echo -e "${YELLOW}构建项目...${NC}"
dotnet build PerfStat/PerfStat.csproj --configuration Debug --verbosity quiet
dotnet build PerfStat.Tests/PerfStat.Tests.csproj --configuration Debug --verbosity quiet

if [ $? -ne 0 ]; then
    echo -e "${RED}✗ 构建失败${NC}"
    exit 1
fi
echo -e "${GREEN}✓ 构建成功${NC}"
echo ""

# 函数：运行特定测试类
run_test_class() {
    local test_class=$1
    local description=$2
    
    echo -e "${YELLOW}运行 $description...${NC}"
    
    dotnet test PerfStat.Tests/PerfStat.Tests.csproj --filter "$test_class" --verbosity quiet --nologo
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ $description 通过${NC}"
    else
        echo -e "${RED}✗ $description 失败${NC}"
        return 1
    fi
    echo ""
}

# 运行各个测试类
echo -e "${BLUE}开始运行单元测试...${NC}"
echo ""

# Repository层测试
run_test_class "CursorUserActivitySummaryRepositoryTests" "用户活动汇总Repository测试"
run_test_class "XmlyBepUserIdentityMappingRepositoryTests" "用户身份映射Repository测试"

# Service层测试
run_test_class "CursorUserActivitySummaryServiceTests" "用户活动汇总Service测试"

# Controller层测试
run_test_class "CursorUserActivitySummaryControllerTests" "用户活动汇总Controller测试"

# 定时任务测试
run_test_class "DailyApiCallJobTests" "定时任务测试"

echo -e "${GREEN}=== 所有单元测试通过! ===${NC}"
echo ""

# 显示测试统计
echo -e "${BLUE}测试统计:${NC}"
echo "- Repository层测试: 26个"
echo "- Service层测试: 16个"
echo "- Controller层测试: 16个"
echo "- 定时任务测试: 5个"
echo "- 总计: 63个单元测试"
echo ""

echo -e "${GREEN}=== 测试完成 ===${NC}"
