{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": ""}, "CronJobs": {"DailyApiCall": {"CronExpression": "0 0 10 * * ?", "TimeZone": "Asia/Shanghai", "ApiUrl": "https://api.cursor.com/teams/daily-usage-data", "HttpMethod": "POST", "Headers": {"Content-Type": "application/json", "User-Agent": "Ximalaya-PerfStat-CronJob/1.0", "Authorization": "Bearer a2V5Xzk4Y2IwMjc4ZDM0MGFiNjQ1ZTc1YTkzMDg2ZWViZWE2ZmI2NTZjYWYyMzYyMDVmMGRmNWM4NDRmMDgzMzk4MWM6"}}}}