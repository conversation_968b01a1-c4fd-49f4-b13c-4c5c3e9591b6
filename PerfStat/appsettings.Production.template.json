{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": ""}, "CronJobs": {"DailyApiCall": {"CronExpression": "0 0 10 * * ?", "TimeZone": "Asia/Shanghai", "ApiUrl": "https://your-production-api.com/api/testapi/daily-task", "HttpMethod": "POST", "Headers": {"Content-Type": "application/json", "User-Agent": "PerfStat-CronJob/1.0"}, "RequestBody": "{\"source\":\"cron-job\",\"timestamp\":\"{{timestamp}}\"}"}}}