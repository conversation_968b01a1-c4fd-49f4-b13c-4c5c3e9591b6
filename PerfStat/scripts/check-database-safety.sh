#!/bin/bash

# 数据库安全检查脚本
# 检查项目中是否存在可能执行DDL操作的代码

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 数据库安全检查 ===${NC}"
echo ""

# 检查项目根目录
if [ ! -f "../PerfStat.csproj" ]; then
    echo -e "${RED}错误: 请在 PerfStat/scripts 目录中运行此脚本${NC}"
    exit 1
fi

# 定义要检查的危险操作
DANGEROUS_OPERATIONS=(
    "EnsureCreated"
    "EnsureDeleted"
    "Database.Migrate"
    "ExecuteSqlRaw.*CREATE"
    "ExecuteSqlRaw.*DROP"
    "ExecuteSqlRaw.*ALTER"
    "ExecuteSqlRaw.*TRUNCATE"
)

# 检查结果
ISSUES_FOUND=0

echo -e "${YELLOW}检查可能的DDL操作...${NC}"
echo ""

# 遍历所有C#文件
for pattern in "${DANGEROUS_OPERATIONS[@]}"; do
    echo -e "${BLUE}检查: ${pattern}${NC}"
    
    # 在项目文件中搜索
    RESULTS=$(find .. -name "*.cs" -not -path "*/bin/*" -not -path "*/obj/*" | xargs grep -n "$pattern" || true)
    
    if [ -n "$RESULTS" ]; then
        echo -e "${RED}⚠️  发现潜在的DDL操作:${NC}"
        echo "$RESULTS"
        echo ""
        ISSUES_FOUND=$((ISSUES_FOUND + 1))
    else
        echo -e "${GREEN}✓ 未发现 ${pattern}${NC}"
    fi
done

echo ""
echo -e "${YELLOW}检查迁移文件...${NC}"

# 检查是否存在迁移文件
MIGRATION_FILES=$(find .. -name "*Migration*.cs" -o -name "Migrations" -type d || true)
if [ -n "$MIGRATION_FILES" ]; then
    echo -e "${RED}⚠️  发现迁移文件:${NC}"
    echo "$MIGRATION_FILES"
    echo ""
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
else
    echo -e "${GREEN}✓ 未发现迁移文件${NC}"
fi

echo ""
echo -e "${YELLOW}检查配置文件中的DDL设置...${NC}"

# 检查appsettings.json中的迁移设置
MIGRATION_SETTINGS=$(grep -n -i "migrate\|ensure" ../appsettings*.json || true)
if [ -n "$MIGRATION_SETTINGS" ]; then
    echo -e "${RED}⚠️  发现迁移相关配置:${NC}"
    echo "$MIGRATION_SETTINGS"
    echo ""
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
else
    echo -e "${GREEN}✓ 配置文件安全${NC}"
fi

echo ""
echo -e "${YELLOW}检查测试文件中的数据库创建操作...${NC}"

# 检查测试文件中的EnsureCreated（应该只在内存数据库中使用）
TEST_DB_OPERATIONS=$(find ../../../PerfStat.Tests -name "*.cs" 2>/dev/null | xargs grep -n "EnsureCreated\|EnsureDeleted" | grep -v "UseInMemoryDatabase" || true)
if [ -n "$TEST_DB_OPERATIONS" ]; then
    echo -e "${RED}⚠️  测试文件中发现可能的DDL操作:${NC}"
    echo "$TEST_DB_OPERATIONS"
    echo ""
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
else
    echo -e "${GREEN}✓ 测试文件安全${NC}"
fi

echo ""
echo "=================================="

if [ $ISSUES_FOUND -eq 0 ]; then
    echo -e "${GREEN}🎉 数据库安全检查通过！${NC}"
    echo -e "${GREEN}项目严格遵循只执行DML操作的原则${NC}"
    echo ""
    echo -e "${BLUE}允许的操作:${NC}"
    echo "✓ SELECT - 查询数据"
    echo "✓ INSERT - 插入数据"
    echo "✓ UPDATE - 更新数据"
    echo "✓ DELETE - 删除数据"
    echo ""
    echo -e "${BLUE}禁止的操作:${NC}"
    echo "✗ CREATE TABLE - 创建表"
    echo "✗ DROP TABLE - 删除表"
    echo "✗ ALTER TABLE - 修改表结构"
    echo "✗ TRUNCATE - 清空表"
else
    echo -e "${RED}❌ 发现 ${ISSUES_FOUND} 个潜在的数据库安全问题${NC}"
    echo ""
    echo -e "${YELLOW}建议:${NC}"
    echo "1. 移除所有DDL操作代码"
    echo "2. 使用内存数据库进行测试"
    echo "3. 联系DBA进行表结构变更"
    echo "4. 参考 DATABASE_SAFETY_GUIDE.md"
    echo ""
    exit 1
fi

echo ""
echo -e "${GREEN}=== 检查完成 ===${NC}"
