#!/bin/bash

# PerfStat 开发环境 User Secrets 设置脚本
# 使用方法: ./scripts/setup-dev-secrets.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== PerfStat 开发环境配置设置 ===${NC}"
echo ""

# 检查是否在正确的目录
if [ ! -f "PerfStat.csproj" ]; then
    echo -e "${RED}错误: 请在 PerfStat 项目目录中运行此脚本${NC}"
    exit 1
fi

# 初始化 User Secrets（如果尚未初始化）
echo -e "${YELLOW}初始化 User Secrets...${NC}"
dotnet user-secrets init

# 提示用户输入数据库配置
echo -e "${YELLOW}请输入数据库配置信息:${NC}"
echo ""

read -p "数据库服务器地址 (默认: localhost): " DB_SERVER
DB_SERVER=${DB_SERVER:-localhost}

read -p "数据库端口 (默认: 3306): " DB_PORT
DB_PORT=${DB_PORT:-3306}

read -p "数据库名称 (默认: ad_other): " DB_DATABASE
DB_DATABASE=${DB_DATABASE:-ad_other}

read -p "数据库用户名 (默认: root): " DB_USERNAME
DB_USERNAME=${DB_USERNAME:-root}

read -s -p "数据库密码: " DB_PASSWORD
echo ""

# 构建连接字符串
CONNECTION_STRING="Server=${DB_SERVER};Port=${DB_PORT};Database=${DB_DATABASE};Uid=${DB_USERNAME};Pwd=${DB_PASSWORD};CharSet=utf8mb4;"

# 设置 User Secrets
echo -e "${YELLOW}设置数据库连接字符串...${NC}"
dotnet user-secrets set "ConnectionStrings:DefaultConnection" "$CONNECTION_STRING"

# 验证设置
echo -e "${YELLOW}验证配置...${NC}"
echo ""
echo -e "${GREEN}已设置的 User Secrets:${NC}"
dotnet user-secrets list

echo ""
echo -e "${GREEN}=== 配置完成! ===${NC}"
echo ""
echo -e "${BLUE}提示:${NC}"
echo "- User Secrets 已安全存储在您的用户配置文件中"
echo "- 这些敏感信息不会被提交到 git 仓库"
echo "- 如需修改配置，可以重新运行此脚本"
echo ""
echo -e "${BLUE}下一步:${NC}"
echo "- 运行 'dotnet run' 启动应用程序"
echo "- 运行 'dotnet test' 执行测试"
