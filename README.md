# 🚀 PerfStat - Performance Insight Platform

一个基于 .NET 9.0 的高性能数据分析平台，提供用户活动汇总和身份映射功能。

## 📋 功能特性

- 🔍 **用户活动汇总**: 全面的用户行为数据分析
- 🔗 **身份映射**: 用户身份关联和管理
- ⏰ **定时任务**: 基于 Quartz.NET 的 cron 调度系统
- 🔐 **安全配置**: 使用 User Secrets 和环境变量管理敏感信息
- 🛡️ **数据库安全**: 严格执行DML操作，保护远程数据库结构
- 🧪 **完整测试**: 单元测试、集成测试和 E2E 测试

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd performance
```

### 2. 配置开发环境

```bash
cd PerfStat
./scripts/setup-dev-secrets.sh
```

### 3. 运行应用

```bash
dotnet run
```

### 4. 运行测试

```bash
# 运行所有测试
./PerfStat.Tests/run-tests.sh

# 或运行特定测试
dotnet test PerfStat.Tests/
```

## 🔐 安全配置

本项目采用安全的配置管理方式：

- **开发环境**: 使用 .NET User Secrets
- **生产环境**: 使用环境变量
- **测试环境**: 使用内存数据库

详细配置说明请参考 [CONFIGURATION.md](CONFIGURATION.md)

## 📚 文档

- [配置安全指南](CONFIGURATION.md)
- [数据库安全操作指南](DATABASE_SAFETY_GUIDE.md) 🛡️
- [项目架构说明](PerfStat/docs/project-summary.md)
- [测试文档](PerfStat.Tests/README.md)
- [定时任务说明](PerfStat/CronJob-README.md)

## 🛠️ 技术栈

- **.NET 9.0** - 最新的 .NET 框架
- **ASP.NET Core** - Web API 框架
- **Entity Framework Core** - ORM 框架
- **MySQL** - 数据库
- **Quartz.NET** - 定时任务调度
- **AutoMapper** - 对象映射
- **xUnit** - 测试框架

## 📞 支持

如有问题，请查看相关文档或提交 Issue。