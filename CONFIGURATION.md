# 🔐 PerfStat 配置安全指南

本文档说明如何安全地配置 PerfStat 应用程序，避免在代码库中暴露敏感信息。

## 📋 概述

PerfStat 使用分层配置系统来管理敏感信息：
- **开发环境**: 使用 .NET User Secrets
- **生产环境**: 使用环境变量
- **测试环境**: 使用内存数据库

## 🛠️ 开发环境配置

### 1. 初始化 User Secrets

```bash
cd PerfStat
dotnet user-secrets init
```

### 2. 设置数据库连接字符串

```bash
dotnet user-secrets set "ConnectionStrings:DefaultConnection" "Server=YOUR_SERVER;Port=YOUR_PORT;Database=YOUR_DB;Uid=YOUR_USER;Pwd=YOUR_PASSWORD;CharSet=utf8mb4;"
```

### 3. 验证配置

```bash
dotnet user-secrets list
```

## 🚀 生产环境配置

### 环境变量

在生产环境中设置以下环境变量：

```bash
export PERFSTAT_DB_SERVER="your-production-server"
export PERFSTAT_DB_PORT="3306"
export PERFSTAT_DB_DATABASE="your-production-database"
export PERFSTAT_DB_USERNAME="your-production-username"
export PERFSTAT_DB_PASSWORD="your-production-password"
export PERFSTAT_API_URL="https://your-production-api.com/api/testapi/daily-task"
```

### Docker 环境

在 `docker-compose.yml` 中：

```yaml
version: '3.8'
services:
  perfstat:
    image: perfstat:latest
    environment:
      - PERFSTAT_DB_SERVER=db-server
      - PERFSTAT_DB_PORT=3306
      - PERFSTAT_DB_DATABASE=ad_other
      - PERFSTAT_DB_USERNAME=app_user
      - PERFSTAT_DB_PASSWORD=${DB_PASSWORD}
      - PERFSTAT_API_URL=https://api.example.com/api/testapi/daily-task
```

### Kubernetes 环境

创建 Secret：

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: perfstat-secrets
type: Opaque
stringData:
  db-password: "your-secure-password"
  api-url: "https://your-api.com/api/testapi/daily-task"
```

在 Deployment 中引用：

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: perfstat
spec:
  template:
    spec:
      containers:
      - name: perfstat
        image: perfstat:latest
        env:
        - name: PERFSTAT_DB_SERVER
          value: "db-server"
        - name: PERFSTAT_DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: perfstat-secrets
              key: db-password
```

## 📁 配置文件说明

### appsettings.json
- 包含默认配置和环境变量占位符
- 可以安全地提交到版本控制

### appsettings.Development.json
- 仅包含开发环境特定的非敏感配置
- 敏感信息通过 User Secrets 管理

### appsettings.*.template.json
- 配置模板文件，显示所需的配置结构
- 不包含真实的敏感信息

## 🔒 安全最佳实践

### ✅ 推荐做法
- 使用 User Secrets 存储开发环境敏感信息
- 使用环境变量存储生产环境敏感信息
- 定期轮换密码和密钥
- 使用最小权限原则配置数据库用户
- 启用数据库连接加密

### ❌ 避免做法
- 在配置文件中硬编码密码
- 将敏感信息提交到版本控制
- 在日志中记录敏感信息
- 使用默认或弱密码

## 🧪 测试环境

测试环境使用内存数据库，配置在 `appsettings.Testing.json` 中：

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "DataSource=:memory:"
  }
}
```

## 🔍 故障排除

### 常见问题

1. **连接字符串未找到**
   - 检查 User Secrets 是否正确设置
   - 验证环境变量是否正确配置

2. **数据库连接失败**
   - 检查网络连接
   - 验证数据库服务器地址和端口
   - 确认用户名和密码正确

3. **权限错误**
   - 检查数据库用户权限
   - 确认防火墙设置

### 调试命令

```bash
# 查看 User Secrets
dotnet user-secrets list

# 查看环境变量
printenv | grep PERFSTAT

# 测试数据库连接
dotnet run --environment Development
```

## 📞 支持

如果遇到配置问题，请检查：
1. 配置文件语法是否正确
2. 环境变量是否正确设置
3. 网络连接是否正常
4. 数据库权限是否充足
